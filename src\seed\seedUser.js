const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
require('dotenv').config({ path: '../../.env' });

const seedUsers = async () => {
  try {
    // Kết nối MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for seeding users');

    // Xóa dữ liệu cũ
    await User.deleteMany({});
    console.log('Cleared existing users');

    // Dữ liệu user mẫu
    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10)
      },
      {
        username: 'john_doe',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10)
      },
      {
        username: 'jane_smith',
        email: '<EMAIL>',
        password: await bcrypt.hash('mypassword', 10)
      },
      {
        username: 'mike_wilson',
        email: '<EMAIL>',
        password: await bcrypt.hash('securepass', 10)
      },
      {
        username: 'sarah_jones',
        email: '<EMAIL>',
        password: await bcrypt.hash('password456', 10)
      }
    ];

    // Thêm users vào database
    const createdUsers = await User.insertMany(users);
    console.log(`✅ Created ${createdUsers.length} users successfully`);

    // In thông tin users đã tạo
    createdUsers.forEach(user => {
      console.log(`- ${user.username} (${user.email}) - ID: ${user._id}`);
    });

    mongoose.connection.close();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error seeding users:', error);
    process.exit(1);
  }
};

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedUsers();
}

module.exports = seedUsers;