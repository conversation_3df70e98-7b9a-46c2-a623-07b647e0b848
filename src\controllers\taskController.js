
const Category = require('../models/Category');
const multer = require('multer');
const path = require('path');
const Task = require('../models/Task');
// C<PERSON>u hình <PERSON>lter để lưu ảnh
const storage = multer.diskStorage({
  destination: './uploads/',
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  },
});
const upload = multer({ storage });

// Tạo task mới
const createTask = async (req, res) => {
  const { title, description, categoryId, startDate, deadline, priority } = req.body;
  const imagePath = req.file ? req.file.path : null;

  try {
    // Kiểm tra category có tồn tại không
    const category = await Category.findOne({ 
      _id: categoryId, 
      userId: userId 
    });
    
    if (!category) {
      return res.status(400).json({ message: 'Category not found' });
    }

    const task = new Task({
      title: title.trim(),
      description,
      categoryId,
      startDate: startDate || Date.now(),
      deadline,
      priority,
      imagePath,
      userId: userId,
    });

    await task.save();
    
    // Populate category info
    await task.populate('categoryId', 'name color icon');
    
    res.status(201).json(task);
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Lấy danh sách tasks với filter, search, pagination
const getTasks = async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    categoryId, 
    search, 
    isCompleted, 
    priority,
    sortBy = 'deadline', 
    order = 'asc' 
  } = req.query;

  try {
    // Xây dựng query
    const query = { userId: userId };
    
    // Filter theo category
    if (categoryId && categoryId !== 'all') {
      query.categoryId = categoryId;
    }
    
    // Filter theo trạng thái hoàn thành
    if (isCompleted !== undefined) {
      query.isCompleted = isCompleted === 'true';
    }
    
    // Filter theo priority
    if (priority) {
      query.priority = priority;
    }
    
    // Search trong title và description
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Sắp xếp
    const sortOrder = order === 'desc' ? -1 : 1;
    const sortOptions = { [sortBy]: sortOrder };

    // Thực hiện query với pagination
    const tasks = await Task.find(query)
      .populate('categoryId', 'name color icon')
      .sort(sortOptions)
      .skip((page - 1) * limit)
      .limit(Number(limit));

    const total = await Task.countDocuments(query);
    
    res.json({ 
      tasks, 
      total, 
      page: Number(page), 
      limit: Number(limit),
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Lấy task theo ID
const getTaskById = async (req, res) => {
  const { taskId } = req.params;
  
  try {
    const task = await Task.findOne({ 
      _id: taskId, 
      userId: userId 
    }).populate('categoryId', 'name color icon');
    
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    res.json(task);
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Cập nhật task
const updateTask = async (req, res) => {
  const { taskId } = req.params;
  const { title, description, categoryId, startDate, deadline, priority } = req.body;
  const imagePath = req.file ? req.file.path : null;

  try {
    const task = await Task.findOne({ _id: taskId, userId: userId });
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Kiểm tra category nếu có thay đổi
    if (categoryId && categoryId !== task.categoryId.toString()) {
      const category = await Category.findOne({ 
        _id: categoryId, 
        userId: userId 
      });
      
      if (!category) {
        return res.status(400).json({ message: 'Category not found' });
      }
    }

    // Cập nhật các field
    if (title) task.title = title.trim();
    if (description !== undefined) task.description = description;
    if (categoryId) task.categoryId = categoryId;
    if (startDate) task.startDate = startDate;
    if (deadline) task.deadline = deadline;
    if (priority) task.priority = priority;
    if (imagePath) task.imagePath = imagePath;

    await task.save();
    await task.populate('categoryId', 'name color icon');
    
    res.json(task);
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Toggle trạng thái hoàn thành
const toggleTaskStatus = async (req, res) => {
  const { taskId } = req.params;
  
  try {
    const task = await Task.findOne({ _id: taskId, userId: userId });
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    task.isCompleted = !task.isCompleted;
    await task.save();
    await task.populate('categoryId', 'name color icon');
    
    res.json(task);
  } catch (error) {
    console.error('Toggle task status error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Xóa task
const deleteTask = async (req, res) => {
  const { taskId } = req.params;
  
  try {
    const task = await Task.findOneAndDelete({ _id: taskId, userId: userId });
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }
    
    res.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Thống kê tasks
const getTaskStats = async (req, res) => {
  try {
    const userId = userId;
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(today.getTime() - (today.getDay() * 24 * 60 * 60 * 1000));

    const stats = await Task.aggregate([
      { $match: { userId: userId } },
      {
        $group: {
          _id: null,
          totalTasks: { $sum: 1 },
          completedTasks: {
            $sum: { $cond: [{ $eq: ['$isCompleted', true] }, 1, 0] }
          },
          pendingTasks: {
            $sum: { $cond: [{ $eq: ['$isCompleted', false] }, 1, 0] }
          },
          overdueTasks: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$isCompleted', false] },
                    { $lt: ['$deadline', now] }
                  ]
                },
                1,
                0
              ]
            }
          },
          todayTasks: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gte: ['$deadline', today] },
                    { $lt: ['$deadline', new Date(today.getTime() + 24 * 60 * 60 * 1000)] }
                  ]
                },
                1,
                0
              ]
            }
          },
          thisWeekTasks: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gte: ['$deadline', weekStart] },
                    { $lt: ['$deadline', new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000)] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    const result = stats[0] || {
      totalTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      overdueTasks: 0,
      todayTasks: 0,
      thisWeekTasks: 0
    };

    res.json(result);
  } catch (error) {
    console.error('Get task stats error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  toggleTaskStatus,
  deleteTask,
  getTaskStats,
  upload
};