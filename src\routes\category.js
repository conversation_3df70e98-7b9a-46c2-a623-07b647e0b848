const express = require('express');
const {
  createCategory,
  getCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  getCategoryStats
} = require('../controllers/categoryController');
const authMiddleware = require('../middleware/auth');
const router = express.Router();

// Áp dụng middleware cho tất cả routes
router.use(authMiddleware);

// Category CRUD routes
router.post('/', createCategory);
router.get('/', getCategories);
router.get('/stats', getCategoryStats);
router.get('/:categoryId', getCategoryById);
router.put('/:categoryId', updateCategory);
router.delete('/:categoryId', deleteCategory);

module.exports = router;