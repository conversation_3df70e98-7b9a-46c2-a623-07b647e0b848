const express = require('express');
const {
  createCategory,
  getCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  getCategoryStats
} = require('../controllers/categoryController');
// const authMiddleware = require('../middleware/auth'); // ❌ Bỏ dòng này
const router = express.Router();

// ❌ Bỏ dòng này: router.use(authMiddleware);

// Category CRUD routes
router.post('/', createCategory);
router.get('/', getCategories);
router.get('/stats', getCategoryStats);
router.get('/:categoryId', getCategoryById);
router.put('/:categoryId', updateCategory);
router.delete('/:categoryId', deleteCategory);

module.exports = router;