const taskSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  categoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', required: true },
  startDate: { type: Date, default: Date.now }, // Thời gian bắt đầu
  deadline: { type: Date, required: true },
  isCompleted: { type: Boolean, default: false }, // Trạng thái hoàn thành
  completedAt: { type: Date }, // Thời gian hoàn thành
  priority: { type: String, enum: ['low', 'medium', 'high'], default: 'medium' },
  imagePath: { type: String },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});