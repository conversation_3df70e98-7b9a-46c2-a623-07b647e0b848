const Category = require('../models/Category');
const Task = require('../models/Task');

// Tạo category mới
const createCategory = async (req, res) => {
  const { name, description, color, icon } = req.body;
  
  try {
    // Kiểm tra category đã tồn tại chưa
    const existingCategory = await Category.findOne({ 
      name: name.trim(), 
      userId: req.user.userId 
    });
    
    if (existingCategory) {
      return res.status(400).json({ message: 'Category already exists' });
    }

    const category = new Category({
      name: name.trim(),
      description,
      color,
      icon,
      userId: req.user.userId
    });

    await category.save();
    res.status(201).json(category);
  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Lấy danh sách categories
const getCategories = async (req, res) => {
  try {
    const categories = await Category.find({ userId: req.user.userId })
      .sort({ createdAt: -1 });

    res.json(categories);
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Lấy category theo ID
const getCategoryById = async (req, res) => {
  const { categoryId } = req.params;
  
  try {
    const category = await Category.findOne({ 
      _id: categoryId, 
      userId: req.user.userId 
    });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    res.json(category);
  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Cập nhật category
const updateCategory = async (req, res) => {
  const { categoryId } = req.params;
  const { name, description, color, icon } = req.body;

  try {
    const category = await Category.findOne({ 
      _id: categoryId, 
      userId: req.user.userId 
    });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    // Kiểm tra tên category trùng (nếu thay đổi tên)
    if (name && name.trim() !== category.name) {
      const existingCategory = await Category.findOne({ 
        name: name.trim(), 
        userId: req.user.userId,
        _id: { $ne: categoryId }
      });
      
      if (existingCategory) {
        return res.status(400).json({ message: 'Category name already exists' });
      }
    }

    // Cập nhật các field
    if (name) category.name = name.trim();
    if (description !== undefined) category.description = description;
    if (color) category.color = color;
    if (icon) category.icon = icon;

    await category.save();
    res.json(category);
  } catch (error) {
    console.error('Update category error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Xóa category
const deleteCategory = async (req, res) => {
  const { categoryId } = req.params;

  try {
    const category = await Category.findOne({ 
      _id: categoryId, 
      userId: req.user.userId 
    });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    // Kiểm tra xem có task nào đang sử dụng category này không
    const taskCount = await Task.countDocuments({ 
      categoryId: categoryId,
      userId: req.user.userId 
    });
    
    if (taskCount > 0) {
      return res.status(400).json({ 
        message: `Cannot delete category. ${taskCount} tasks are using this category.` 
      });
    }

    await Category.findByIdAndDelete(categoryId);
    res.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Thống kê categories
const getCategoryStats = async (req, res) => {
  try {
    const stats = await Category.aggregate([
      { $match: { userId: req.user.userId } },
      {
        $lookup: {
          from: 'tasks',
          localField: '_id',
          foreignField: 'categoryId',
          as: 'tasks'
        }
      },
      {
        $project: {
          name: 1,
          color: 1,
          icon: 1,
          totalTasks: { $size: '$tasks' },
          completedTasks: {
            $size: {
              $filter: {
                input: '$tasks',
                cond: { $eq: ['$$this.isCompleted', true] }
              }
            }
          },
          pendingTasks: {
            $size: {
              $filter: {
                input: '$tasks',
                cond: { $eq: ['$$this.isCompleted', false] }
              }
            }
          }
        }
      },
      {
        $addFields: {
          completionRate: {
            $cond: {
              if: { $eq: ['$totalTasks', 0] },
              then: 0,
              else: {
                $round: [
                  { $multiply: [{ $divide: ['$completedTasks', '$totalTasks'] }, 100] },
                  1
                ]
              }
            }
          }
        }
      },
      { $sort: { totalTasks: -1 } }
    ]);

    res.json({ categories: stats });
  } catch (error) {
    console.error('Get category stats error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  createCategory,
  getCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  getCategoryStats
};