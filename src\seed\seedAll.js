const seedUsers = require('./seedUser');
const seedTasks = require('./seedTasks');

const seedAll = async () => {
  try {
    console.log('🚀 Starting database seeding...\n');

    // Seed users trước
    console.log('👥 Seeding users...');
    await seedUsers();
    console.log('✅ Users seeded successfully\n');

    // Đợi 1 giây để đảm bảo users đã được tạo
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Seed tasks sau
    console.log('📝 Seeding tasks...');
    await seedTasks();
    console.log('✅ Tasks seeded successfully\n');

    console.log('🎉 All data seeded successfully!');
    console.log('You can now start the application with: npm run dev');

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  }
};

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedAll();
}

module.exports = seedAll;