const seedUsers = require('./seedUser');
const seedCategories = require('./seedCategory');
const seedTasks = require('./seedTasks');

const seedAll = async () => {
  try {
    console.log('🚀 Starting database seeding...\n');
    
    // 1. Seed users trước
    console.log('👥 Seeding users...');
    await seedUsers();
    console.log('✅ Users seeded successfully\n');
    
    // Đợi 1 giây
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. Seed categories
    console.log('📂 Seeding categories...');
    await seedCategories();
    console.log('✅ Categories seeded successfully\n');
    
    // Đợi 1 giây
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. Seed tasks cuối cùng
    console.log('📝 Seeding tasks...');
    await seedTasks();
    console.log('✅ Tasks seeded successfully\n');
    
    console.log('🎉 All data seeded successfully!');
    console.log('📊 Summary:');
    console.log('- Users: 5 accounts created');
    console.log('- Categories: 8 categories per user');
    console.log('- Tasks: Multiple tasks per category');
    console.log('\nYou can now start the application with: npm run dev');
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  }
};

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedAll();
}

module.exports = seedAll;