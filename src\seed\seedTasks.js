const mongoose = require('mongoose');
const Task = require('../models/Task');
const User = require('../models/User');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const seedTasks = async () => {
  try {
    // Kết nối MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for seeding tasks');

    // Lấy danh sách users để gán tasks
    const users = await User.find({});
    if (users.length === 0) {
      console.log('❌ No users found. Please run seed:users first');
      process.exit(1);
    }

    // Xóa dữ liệu tasks cũ
    await Task.deleteMany({});
    console.log('Cleared existing tasks');

    // Dữ liệu tasks mẫu
    const categories = ['Công việc', '<PERSON><PERSON><PERSON> tập', '<PERSON><PERSON> nhân', '<PERSON><PERSON> đ<PERSON>', '<PERSON><PERSON><PERSON> khỏe', '<PERSON><PERSON> sắm'];

    const taskTemplates = [
      {
        title: '<PERSON><PERSON><PERSON> thành báo cáo tháng',
        category: 'Công việc',
        description: 'Viết báo cáo tổng kết công việc tháng này và gửi cho quản lý',
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 ngày sau
      },
      {
        title: 'Học Node.js',
        category: 'Học tập',
        description: 'Hoàn thành khóa học Node.js trên Udemy',
        deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) // 14 ngày sau
      },
      {
        title: 'Đọc sách "Clean Code"',
        category: 'Học tập',
        description: 'Đọc và ghi chú những điểm quan trọng trong sách Clean Code',
        deadline: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000) // 21 ngày sau
      },
      {
        title: 'Tập thể dục',
        category: 'Sức khỏe',
        description: 'Tập gym 3 lần trong tuần',
        deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 ngày sau
      },
      {
        title: 'Mua quà sinh nhật mẹ',
        category: 'Gia đình',
        description: 'Chọn và mua quà sinh nhật cho mẹ',
        deadline: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000) // 10 ngày sau
      },
      {
        title: 'Dọn dẹp phòng ngủ',
        category: 'Cá nhân',
        description: 'Sắp xếp lại đồ đạc và dọn dẹp phòng ngủ',
        deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) // 2 ngày sau
      },
      {
        title: 'Mua thực phẩm tuần này',
        category: 'Mua sắm',
        description: 'Đi siêu thị mua thực phẩm cho cả tuần',
        deadline: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000) // 1 ngày sau
      },
      {
        title: 'Chuẩn bị presentation',
        category: 'Công việc',
        description: 'Làm slide presentation cho cuộc họp tuần tới',
        deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) // 5 ngày sau
      },
      {
        title: 'Học tiếng Anh',
        category: 'Học tập',
        description: 'Học 30 từ vựng mới và luyện speaking',
        deadline: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000) // 1 ngày sau
      },
      {
        title: 'Khám sức khỏe định kỳ',
        category: 'Sức khỏe',
        description: 'Đặt lịch và đi khám sức khỏe tổng quát',
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 ngày sau
      }
    ];

    // Tạo tasks cho mỗi user
    const tasks = [];
    users.forEach((user, userIndex) => {
      // Mỗi user sẽ có 3-5 tasks ngẫu nhiên
      const numTasks = Math.floor(Math.random() * 3) + 3; // 3-5 tasks
      const userTasks = [];

      for (let i = 0; i < numTasks; i++) {
        const randomTemplate = taskTemplates[Math.floor(Math.random() * taskTemplates.length)];
        const task = {
          ...randomTemplate,
          title: `${randomTemplate.title} - ${user.username}`,
          userId: user._id,
          createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // Random trong 7 ngày qua
        };
        userTasks.push(task);
      }

      tasks.push(...userTasks);
    });

    // Thêm tasks vào database
    const createdTasks = await Task.insertMany(tasks);
    console.log(`✅ Created ${createdTasks.length} tasks successfully`);

    // In thống kê
    console.log('\n📊 Task Statistics:');
    users.forEach(user => {
      const userTasks = createdTasks.filter(task => task.userId.toString() === user._id.toString());
      console.log(`- ${user.username}: ${userTasks.length} tasks`);
    });

    // In tasks theo category
    console.log('\n📋 Tasks by Category:');
    categories.forEach(category => {
      const categoryTasks = createdTasks.filter(task => task.category === category);
      console.log(`- ${category}: ${categoryTasks.length} tasks`);
    });

    mongoose.connection.close();
    console.log('\nDatabase connection closed');
  } catch (error) {
    console.error('Error seeding tasks:', error);
    process.exit(1);
  }
};

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedTasks();
}

module.exports = seedTasks;