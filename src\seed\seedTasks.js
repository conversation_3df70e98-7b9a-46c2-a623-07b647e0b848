const mongoose = require('mongoose');
const Task = require('../models/Task');
const Category = require('../models/Category');
const User = require('../models/User');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const seedTasks = async () => {
  try {
    // Kết nối MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for seeding tasks');

    // Lấy danh sách users và categories
    const users = await User.find({});
    const categories = await Category.find({});
    
    if (users.length === 0) {
      console.log('❌ No users found. Please run seed:users first');
      process.exit(1);
    }
    
    if (categories.length === 0) {
      console.log('❌ No categories found. Please run seed:categories first');
      process.exit(1);
    }

    // <PERSON><PERSON>a dữ liệu tasks cũ
    await Task.deleteMany({});
    console.log('Cleared existing tasks');

    // Dữ liệu tasks mẫu theo category
    const taskTemplatesByCategory = {
      'Công việc': [
        {
          title: 'Hoàn thành báo cáo tháng',
          description: 'Viết báo cáo tổng kết công việc tháng này và gửi cho quản lý',
          priority: 'high',
          daysFromNow: 7
        },
        {
          title: 'Chuẩn bị presentation',
          description: 'Làm slide presentation cho cuộc họp tuần tới',
          priority: 'medium',
          daysFromNow: 5
        },
        {
          title: 'Review code của team',
          description: 'Kiểm tra và review code của các thành viên trong team',
          priority: 'medium',
          daysFromNow: 3
        }
      ],
      'Học tập': [
        {
          title: 'Học Node.js',
          description: 'Hoàn thành khóa học Node.js trên Udemy',
          priority: 'medium',
          daysFromNow: 14
        },
        {
          title: 'Đọc sách "Clean Code"',
          description: 'Đọc và ghi chú những điểm quan trọng trong sách Clean Code',
          priority: 'low',
          daysFromNow: 21
        },
        {
          title: 'Học tiếng Anh',
          description: 'Học 30 từ vựng mới và luyện speaking',
          priority: 'medium',
          daysFromNow: 1
        }
      ],
      'Sức khỏe': [
        {
          title: 'Tập thể dục',
          description: 'Tập gym 3 lần trong tuần',
          priority: 'high',
          daysFromNow: 3
        },
        {
          title: 'Khám sức khỏe định kỳ',
          description: 'Đặt lịch và đi khám sức khỏe tổng quát',
          priority: 'medium',
          daysFromNow: 30
        }
      ],
      'Gia đình': [
        {
          title: 'Mua quà sinh nhật mẹ',
          description: 'Chọn và mua quà sinh nhật cho mẹ',
          priority: 'high',
          daysFromNow: 10
        },
        {
          title: 'Gọi điện cho bà ngoại',
          description: 'Gọi điện thăm hỏi sức khỏe bà ngoại',
          priority: 'medium',
          daysFromNow: 2
        }
      ],
      'Cá nhân': [
        {
          title: 'Dọn dẹp phòng ngủ',
          description: 'Sắp xếp lại đồ đạc