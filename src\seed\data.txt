Method: POST
URL: http://localhost:5000/api/auth/register
Headers: Content-Type: application/json

Body (JSON):
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "123456"
}

Response Success (201):
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

Response Error (400):
{
  "message": "User already exists"
}


Method: POST
URL: http://localhost:5000/api/auth/login
Headers: Content-Type: application/json

Body (JSON):
{
  "email": "<EMAIL>",
  "password": "123456"
}

Response Success (200):
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

Response Error (400):
{
  "message": "Invalid credentials"
}



Method: GET
URL: http://localhost:5000/api/categories

Response Success (200):
[
  {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "name": "Công việc",
    "description": "<PERSON>ác task liên quan đến công việc",
    "color": "#e74c3c",
    "icon": "briefcase",
    "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "createdAt": "2024-01-01T10:00:00.000Z",
    "updatedAt": "2024-01-01T10:00:00.000Z"
  }
]



Method: POST
URL: http://localhost:5000/api/categories
Headers: Content-Type: application/json

Body (JSON):
{
  "name": "Học tập",
  "description": "Các hoạt động học tập",
  "color": "#3498db",
  "icon": "book",
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1"
}

Response Success (201):
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
  "name": "Học tập",
  "description": "Các hoạt động học tập",
  "color": "#3498db",
  "icon": "book",
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "createdAt": "2024-01-01T10:00:00.000Z",
  "updatedAt": "2024-01-01T10:00:00.000Z"
}

Response Error (400):
{
  "message": "Category already exists"
}


Method: GET
URL: http://localhost:5000/api/categories/64f8a1b2c3d4e5f6a7b8c9d2

Response Success (200):
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
  "name": "Học tập",
  "description": "Các hoạt động học tập",
  "color": "#3498db",
  "icon": "book",
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "createdAt": "2024-01-01T10:00:00.000Z",
  "updatedAt": "2024-01-01T10:00:00.000Z"
}

Response Error (404):
{
  "message": "Category not found"
}



Method: PUT
URL: http://localhost:5000/api/categories/64f8a1b2c3d4e5f6a7b8c9d2
Headers: Content-Type: application/json

Body (JSON):
{
  "name": "Học tập nâng cao",
  "description": "Các hoạt động học tập chuyên sâu",
  "color": "#2980b9",
  "icon": "graduation-cap"
}

Response Success (200):
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
  "name": "Học tập nâng cao",
  "description": "Các hoạt động học tập chuyên sâu",
  "color": "#2980b9",
  "icon": "graduation-cap",
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "createdAt": "2024-01-01T10:00:00.000Z",
  "updatedAt": "2024-01-01T10:30:00.000Z"
}



Method: DELETE
URL: http://localhost:5000/api/categories/64f8a1b2c3d4e5f6a7b8c9d2

Response Success (200):
{
  "message": "Category deleted successfully"
}

Response Error (400):
{
  "message": "Cannot delete category. 5 tasks are using this category."
}

Response Error (404):
{
  "message": "Category not found"
}



Method: GET
URL: http://localhost:5000/api/categories/stats

Response Success (200):
{
  "categories": [
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "name": "Công việc",
      "color": "#e74c3c",
      "icon": "briefcase",
      "totalTasks": 10,
      "completedTasks": 6,
      "pendingTasks": 4,
      "completionRate": 60.0
    },
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
      "name": "Học tập",
      "color": "#3498db",
      "icon": "book",
      "totalTasks": 5,
      "completedTasks": 2,
      "pendingTasks": 3,
      "completionRate": 40.0
    }
  ]
}



Method: GET
URL: http://localhost:5000/api/tasks

Query Parameters (tùy chọn):
- page=1
- limit=10
- categoryId=64f8a1b2c3d4e5f6a7b8c9d0
- search=keyword
- isCompleted=true/false
- priority=high/medium/low
- sortBy=deadline/createdAt/title
- order=asc/desc

Example: http://localhost:5000/api/tasks?page=1&limit=5&categoryId=64f8a1b2c3d4e5f6a7b8c9d0&isCompleted=false

Response Success (200):
{
  "tasks": [
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d3",
      "title": "Hoàn thành báo cáo",
      "description": "Viết báo cáo tháng",
      "categoryId": {
        "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "name": "Công việc",
        "color": "#e74c3c",
        "icon": "briefcase"
      },
      "startDate": "2024-01-01T10:00:00.000Z",
      "deadline": "2024-01-07T10:00:00.000Z",
      "isCompleted": false,
      "priority": "high",
      "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-01T10:00:00.000Z"
    }
  ],
  "total": 15,
  "page": 1,
  "limit": 5,
  "totalPages": 3
}



Method: POST
URL: http://localhost:5000/api/tasks
Headers: Content-Type: application/json

Body (JSON):
{
  "title": "Học Node.js",
  "description": "Hoàn thành khóa học Node.js",
  "categoryId": "64f8a1b2c3d4e5f6a7b8c9d2",
  "startDate": "2024-01-01T10:00:00.000Z",
  "deadline": "2024-01-15T10:00:00.000Z",
  "priority": "medium",
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1"
}

Response Success (201):
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d4",
  "title": "Học Node.js",
  "description": "Hoàn thành khóa học Node.js",
  "categoryId": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
    "name": "Học tập",
    "color": "#3498db",
    "icon": "book"
  },
  "startDate": "2024-01-01T10:00:00.000Z",
  "deadline": "2024-01-15T10:00:00.000Z",
  "isCompleted": false,
  "priority": "medium",
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "createdAt": "2024-01-01T10:00:00.000Z",
  "updatedAt": "2024-01-01T10:00:00.000Z"
}


Method: POST
URL: http://localhost:5000/api/tasks
Headers: Content-Type: multipart/form-data

Body (Form-data):
- title: "Task với hình ảnh"
- description: "Task có kèm hình ảnh"
- categoryId: "64f8a1b2c3d4e5f6a7b8c9d2"
- deadline: "2024-01-15T10:00:00.000Z"
- priority: "high"
- userId: "64f8a1b2c3d4e5f6a7b8c9d1"
- image: [FILE] (chọn file ảnh)

Response Success (201):
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d5",
  "title": "Task với hình ảnh",
  "description": "Task có kèm hình ảnh",
  "imagePath": "uploads/1704110400000-image.jpg",
  ...
}



Method: GET
URL: http://localhost:5000/api/tasks/64f8a1b2c3d4e5f6a7b8c9d4

Response Success (200):
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d4",
  "title": "Học Node.js",
  "description": "Hoàn thành khóa học Node.js",
  "categoryId": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
    "name": "Học tập",
    "color": "#3498db",
    "icon": "book"
  },
  "startDate": "2024-01-01T10:00:00.000Z",
  "deadline": "2024-01-15T10:00:00.000Z",
  "isCompleted": false,
  "priority": "medium",
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "createdAt": "2024-01-01T10:00:00.000Z",
  "updatedAt": "2024-01-01T10:00:00.000Z"
}



Method: PUT
URL: http://localhost:5000/api/tasks/64f8a1b2c3d4e5f6a7b8c9d4
Headers: Content-Type: application/json

Body (JSON):
{
  "title": "Học Node.js nâng cao",
  "description": "Hoàn thành khóa học Node.js và làm project",
  "deadline": "2024-01-20T10:00:00.000Z",
  "priority": "high"
}

Response Success (200):
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d4",
  "title": "Học Node.js nâng cao",
  "description": "Hoàn thành khóa học Node.js và làm project",
  "deadline": "2024-01-20T10:00:00.000Z",
  "priority": "high",
  "updatedAt": "2024-01-01T11:00:00.000Z",
  ...
}



Method: PATCH
URL: http://localhost:5000/api/tasks/64f8a1b2c3d4e5f6a7b8c9d4

Response Success (200):
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d4",
  "title": "Học Node.js nâng cao",
  "isCompleted": true,
  "completedAt": "2024-01-01T11:30:00.000Z",
  "updatedAt": "2024-01-01T11:30:00.000Z",
  ...
}


Method: GET
URL: http://localhost:5000/api/tasks/stats

Response Success (200):
{
  "totalTasks": 25,
  "completedTasks": 15,
  "pendingTasks": 10,
  "overdueTasks": 3,
  "todayTasks": 5,
  "thisWeekTasks": 12
}



Method: GET
URL: http://localhost:5000/api/health

Response Success (200):
{
  "status": "OK",
  "message": "Todo API is running",
  "timestamp": "2024-01-01T10:00:00.000Z"
}