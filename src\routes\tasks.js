const express = require('express');
const {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  toggleTaskStatus,
  deleteTask,
  getTaskStats,
  upload
} = require('../controllers/taskController');
// const authMiddleware = require('../middleware/auth'); // ❌ Bỏ dòng này
const router = express.Router();

// ❌ Bỏ dòng này: router.use(authMiddleware);

// Task routes
router.post('/', upload.single('image'), createTask);
router.get('/', getTasks);
router.get('/stats', getTaskStats);
router.get('/:taskId', getTaskById);
router.put('/:taskId', upload.single('image'), updateTask);
router.patch('/:taskId/toggle', toggleTaskStatus);
router.delete('/:taskId', deleteTask);

module.exports = router;