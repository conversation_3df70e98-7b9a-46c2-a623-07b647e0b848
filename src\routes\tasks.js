const express = require('express');
const {
  createTask,
  getTasks,
  getTaskById,
  updateTask,
  toggleTaskStatus,
  deleteTask,
  getTaskStats,
  upload
} = require('../controllers/taskController');
const router = express.Router();

// Task routes
router.post('/', createTask);
router.get('/', getTasks);
router.get('/stats', getTaskStats);
router.get('/:taskId', getTaskById);
router.put('/:taskId', upload.single('image'), updateTask);
router.patch('/:taskId/toggle', toggleTaskStatus);
router.delete('/:taskId', deleteTask);

module.exports = router;